/* ===== MOBILE-FIRST RESPONSIVE THEME =====
 *
 * Breakpoint System:
 * - Mobile (Base):     0px - 480px    (default styles)
 * - Phone Large:       481px+         (larger phones)
 * - Tablet:            768px+         (tablets, small laptops)
 * - Desktop:           1024px+        (desktops, large laptops)
 * - Full HD:           1440px+        (1440p monitors)
 * - Large:             1920px+        (1080p+ monitors)
 * - Ultra Large:       2560px+        (4K+ monitors)
 *
 * Design Principles:
 * - Mobile-first approach with min-width media queries
 * - Touch-friendly targets (44px minimum)
 * - Responsive typography scaling
 * - Flexible grid systems
 * - Optimized for performance and accessibility
 */

/* CSS Reset and Normalization */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 14px; /* Mobile-first: smaller base font size */
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%; /* Prevent iOS font scaling */
  -ms-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: color 0.2s ease, background-color 0.2s ease;
  overflow-x: hidden; /* Prevent horizontal scroll on mobile */
}

/* Theme Variables */
:root {
  /* Light Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-elevated: #ffffff;

  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-tertiary: #adb5bd;
  --text-inverse: #ffffff;

  --border-primary: #dee2e6;
  --border-secondary: #e9ecef;
  --border-focus: #0d6efd;

  --accent-primary: #0d6efd;
  --accent-primary-hover: #0b5ed7;
  --accent-secondary: #6c757d;
  --accent-secondary-hover: #5c636a;

  --success: #198754;
  --success-hover: #157347;
  --warning: #fd7e14;
  --warning-hover: #e8681c;
  --danger: #dc3545;
  --danger-hover: #bb2d3b;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  --radius: 6px;
  --radius-sm: 4px;
  --radius-lg: 8px;

  /* Responsive Spacing (Mobile-first) */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Container Widths */
  --container-sm: 100%;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1200px;
  --container-2xl: 1400px;
  --container-3xl: 1600px;

  /* Touch Targets (minimum 44px for accessibility) */
  --touch-target: 44px;
  --touch-target-sm: 40px;
  --touch-target-lg: 48px;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #0d1117;
  --bg-secondary: #161b22;
  --bg-tertiary: #21262d;
  --bg-elevated: #161b22;

  --text-primary: #f0f6fc;
  --text-secondary: #8b949e;
  --text-tertiary: #6e7681;
  --text-inverse: #0d1117;

  --border-primary: #30363d;
  --border-secondary: #21262d;
  --border-focus: #58a6ff;

  --accent-primary: #58a6ff;
  --accent-primary-hover: #79c0ff;
  --accent-secondary: #8b949e;
  --accent-secondary-hover: #b1bac4;

  --success: #3fb950;
  --success-hover: #56d364;
  --warning: #d29922;
  --warning-hover: #e3b341;
  --danger: #f85149;
  --danger-hover: #ff7b72;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
}

/* Typography - Mobile First */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

/* Mobile typography sizes */
h1 { font-size: 1.75rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }
h4 { font-size: 1.125rem; }
h5 { font-size: 1rem; }
h6 { font-size: 0.875rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Links */
a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-primary-hover);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Buttons - Mobile First */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  min-height: var(--touch-target); /* Ensure touch-friendly size */
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border: 1px solid transparent;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent; /* Remove iOS tap highlight */
}

.btn:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--accent-primary);
  color: var(--text-inverse);
  border-color: var(--accent-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--accent-primary-hover);
  border-color: var(--accent-primary-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  border-color: var(--border-primary);
}

.btn-outline {
  background-color: transparent;
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--accent-primary);
  color: var(--text-inverse);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  min-height: var(--touch-target-sm);
  font-size: 0.8125rem;
}

.btn-lg {
  padding: var(--spacing-sm) var(--spacing-lg);
  min-height: var(--touch-target-lg);
  font-size: 1rem;
}

/* Navigation - Mobile First */
nav {
  background-color: var(--bg-elevated);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: var(--container-sm);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: var(--touch-target-lg);
}

.nav-brand {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  list-style: none;
  flex-wrap: wrap;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  min-height: var(--touch-target-sm);
  display: flex;
  align-items: center;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;
}

.nav-link:hover {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  text-decoration: none;
}

.nav-link.active {
  color: var(--accent-primary);
  background-color: var(--bg-secondary);
}

/* Search */
.search-container {
  position: relative;
  display: inline-block;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.search-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgb(13 110 253 / 0.1);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

/* Sections - Mobile First */
section {
  padding: var(--spacing-xl) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: 1.75rem; /* Mobile size */
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.section-subtitle {
  font-size: 1rem; /* Mobile size */
  color: var(--text-secondary);
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.container {
  max-width: var(--container-sm);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  width: 100%;
}

/* Cards - Mobile First */
.card {
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  width: 100%;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-header {
  padding: var(--spacing-md) var(--spacing-md) 0;
}

.card-body {
  padding: var(--spacing-md);
}

.card-footer {
  padding: 0 var(--spacing-md) var(--spacing-md);
  border-top: 1px solid var(--border-secondary);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
}

/* Footer - Mobile First */
footer {
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: var(--spacing-2xl) 0 var(--spacing-xl);
  margin-top: var(--spacing-3xl);
}

.footer-content {
  max-width: var(--container-sm);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: grid;
  grid-template-columns: 1fr; /* Mobile: single column */
  gap: var(--spacing-xl);
  text-align: center; /* Mobile: center align */
}

.footer-section h4 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--text-secondary);
  font-size: 0.875rem;
  padding: var(--spacing-xs);
  display: inline-block;
  min-height: var(--touch-target-sm);
  -webkit-tap-highlight-color: transparent;
}

.footer-links a:hover {
  color: var(--text-primary);
}

.footer-bottom {
  border-top: 1px solid var(--border-primary);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  text-align: center;
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

/* Form Elements - Mobile First */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm);
  min-height: var(--touch-target);
  font-size: 1rem; /* Prevent zoom on iOS */
  border: 1px solid var(--border-primary);
  border-radius: var(--radius);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
  -webkit-appearance: none; /* Remove iOS styling */
  appearance: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgb(13 110 253 / 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px; /* Larger for mobile */
  line-height: 1.5;
}

/* Utility Classes - Mobile First */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Responsive spacing using CSS variables */
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }
.d-grid { display: grid; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* Responsive grid utilities */
.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Mobile-specific utilities */
.mobile-only { display: block; }
.desktop-only { display: none; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Touch-friendly utilities */
.touch-target { min-height: var(--touch-target); }
.touch-target-sm { min-height: var(--touch-target-sm); }
.touch-target-lg { min-height: var(--touch-target-lg); }

/* Theme Toggle Button */
.theme-toggle {
  background: none;
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== RESPONSIVE BREAKPOINTS - MOBILE FIRST ===== */

/* Phone Large: 481px and up */
@media (min-width: 481px) {
  html {
    font-size: 15px;
  }

  .nav-brand {
    font-size: 1.25rem;
  }

  .nav-links {
    gap: var(--spacing-md);
  }

  .section-subtitle {
    font-size: 1.125rem;
    max-width: 480px;
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-lg);
  }

  .card-footer {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    padding-top: var(--spacing-lg);
  }
}

/* Tablet: 768px and up */
@media (min-width: 768px) {
  html {
    font-size: 16px;
  }

  .container {
    max-width: var(--container-md);
    padding: 0 var(--spacing-xl);
  }

  .nav-container {
    max-width: var(--container-md);
    padding: 0 var(--spacing-xl);
  }

  .nav-links {
    gap: var(--spacing-lg);
  }

  /* Typography scaling */
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }
  h5 { font-size: 1.125rem; }
  h6 { font-size: 1rem; }

  .section-title {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1.125rem;
    max-width: 600px;
  }

  section {
    padding: var(--spacing-3xl) 0;
  }

  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    text-align: left;
    gap: var(--spacing-xl);
  }

  .footer-content {
    max-width: var(--container-md);
    padding: 0 var(--spacing-xl);
  }

  /* Responsive utilities for tablet+ */
  .mobile-only { display: none; }
  .desktop-only { display: block; }

  .tablet-grid-2 { grid-template-columns: repeat(2, 1fr); }
  .tablet-grid-3 { grid-template-columns: repeat(3, 1fr); }

  .tablet-flex-row { flex-direction: row; }
  .tablet-text-left { text-align: left; }
}

/* Desktop: 1024px and up */
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }

  .nav-container {
    max-width: var(--container-lg);
  }

  .footer-content {
    max-width: var(--container-lg);
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  /* Enhanced typography for desktop */
  h1 { font-size: 2.25rem; }
  h2 { font-size: 1.875rem; }

  .section-title {
    font-size: 2.25rem;
  }

  .nav-links {
    gap: var(--spacing-xl);
  }

  /* Desktop responsive utilities */
  .desktop-grid-3 { grid-template-columns: repeat(3, 1fr); }
  .desktop-grid-4 { grid-template-columns: repeat(4, 1fr); }

  .desktop-flex-row { flex-direction: row; }
  .desktop-justify-between { justify-content: space-between; }
}

/* Full HD: 1440px and up */
@media (min-width: 1440px) {
  .container {
    max-width: var(--container-xl);
  }

  .nav-container {
    max-width: var(--container-xl);
  }

  .footer-content {
    max-width: var(--container-xl);
  }

  /* Larger typography for Full HD */
  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }

  .section-title {
    font-size: 2.5rem;
  }

  .section-subtitle {
    font-size: 1.25rem;
    max-width: 700px;
  }
}

/* Large: 1920px and up */
@media (min-width: 1920px) {
  .container {
    max-width: var(--container-2xl);
  }

  .nav-container {
    max-width: var(--container-2xl);
  }

  .footer-content {
    max-width: var(--container-2xl);
  }

  /* Enhanced spacing for large screens */
  section {
    padding: 5rem 0;
  }

  .section-header {
    margin-bottom: 4rem;
  }
}

/* Ultra Large: 2560px and up */
@media (min-width: 2560px) {
  .container {
    max-width: var(--container-3xl);
  }

  .nav-container {
    max-width: var(--container-3xl);
  }

  .footer-content {
    max-width: var(--container-3xl);
  }

  /* Maximum typography scaling */
  h1 { font-size: 3rem; }
  h2 { font-size: 2.5rem; }
  h3 { font-size: 2rem; }

  .section-title {
    font-size: 3rem;
  }

  .section-subtitle {
    font-size: 1.5rem;
    max-width: 800px;
  }

  /* Enhanced spacing for ultra-large screens */
  section {
    padding: 6rem 0;
  }

  .section-header {
    margin-bottom: 5rem;
  }
}
