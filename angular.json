{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"angular-guide": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "angular-guide:build:production"}, "development": {"buildTarget": "angular-guide:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css"]}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"configDir": ".storybook", "browserTarget": "angular-guide:build", "experimentalZoneless": true, "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "port": 6006}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"configDir": ".storybook", "browserTarget": "angular-guide:build", "experimentalZoneless": true, "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "outputDir": "storybook-static"}}}}}}